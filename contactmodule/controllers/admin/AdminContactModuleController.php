<?php
/**
 * Admin Controller pro Contact Module
 * Správa kontaktních údajů v administraci PrestaShop
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminContactModuleController extends ModuleAdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->meta_title = 'Správa kontaktních údajů';
    }

    public function initContent()
    {
        parent::initContent();

        $output = '';

        // Zpracování formuláře
        if (Tools::isSubmit('submitContactModule')) {
            $output .= $this->processForm();
        }

        // Zobrazení obsahu ve stejném stylu jako původní konfigurace
        $output .= $this->displayForm();

        $this->content = $output;
        $this->context->smarty->assign('content', $this->content);
    }

    private function displayForm()
    {
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);
        if (!$representatives) {
            $representatives = [];
        }

        $output = '<div class="panel">
            <div class="panel-heading">
                <i class="icon-cogs"></i> Nastavení kontaktní stránky
            </div>
            <div class="panel-body">
                <form id="configuration_form" class="defaultForm form-horizontal" method="post">
                    <input type="hidden" name="token" value="' . Tools::getAdminTokenLite('AdminContactModule') . '">

                    <div class="form-group">
                        <label class="control-label col-lg-3">Název společnosti</label>
                        <div class="col-lg-9">
                            <input type="text" name="company_name" value="' . htmlspecialchars(Configuration::get('CONTACT_COMPANY_NAME')) . '" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Adresa</label>
                        <div class="col-lg-9">
                            <textarea name="address" rows="3" class="form-control">' . htmlspecialchars(Configuration::get('CONTACT_ADDRESS')) . '</textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Telefon</label>
                        <div class="col-lg-9">
                            <input type="text" name="phone" value="' . htmlspecialchars(Configuration::get('CONTACT_PHONE')) . '" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Email</label>
                        <div class="col-lg-9">
                            <input type="email" name="email" value="' . htmlspecialchars(Configuration::get('CONTACT_EMAIL')) . '" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Mapa (embed kód)</label>
                        <div class="col-lg-9">
                            <textarea name="map_embed" rows="5" class="form-control" placeholder="Vložte iframe kód z Google Maps nebo Mapy.cz">' . htmlspecialchars(Configuration::get('CONTACT_MAP_EMBED')) . '</textarea>
                            <p class="help-block">Vložte celý iframe kód včetně &lt;iframe&gt; tagů. Podporuje Google Maps i Mapy.cz</p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-lg-3">Bankovní spojení</label>
                        <div class="col-lg-9">
                            <textarea name="bank_info" rows="3" class="form-control">' . htmlspecialchars(Configuration::get('CONTACT_BANK_INFO')) . '</textarea>
                        </div>
                    </div>

                    <hr>
                    <h4><i class="icon-users"></i> Obchodní zástupci</h4>
                    <p class="help-block">Seznam obchodních zástupců</p>';

        if (!empty($representatives)) {
            $output .= '<div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Jméno</th>
                                    <th>Pozice</th>
                                    <th>Telefon</th>
                                    <th>Email</th>
                                    <th>Oblast</th>
                                </tr>
                            </thead>
                            <tbody>';

            foreach ($representatives as $rep) {
                $output .= '<tr>
                            <td>' . htmlspecialchars($rep['name']) . '</td>
                            <td>' . htmlspecialchars($rep['position']) . '</td>
                            <td>' . htmlspecialchars($rep['phone']) . '</td>
                            <td>' . htmlspecialchars($rep['email']) . '</td>
                            <td>' . htmlspecialchars($rep['region']) . '</td>
                        </tr>';
            }

            $output .= '</tbody>
                        </table>
                    </div>';
        } else {
            $output .= '<p>Žádní obchodní zástupci nejsou nastaveni.</p>';
        }

        $output .= '<p><em>Pro úpravu zástupců použijte standardní konfiguraci modulu v sekci Moduly.</em></p>

                    <div class="panel-footer">
                        <button type="submit" name="submitContactModule" class="btn btn-default pull-right">
                            <i class="process-icon-save"></i> Uložit
                        </button>
                    </div>
                </form>
            </div>
        </div>';

        return $output;
    }



    public function processForm()
    {
        $output = '';

        // Uložení základních údajů - stejné názvy polí jako v původní konfiguraci
        Configuration::updateValue('CONTACT_COMPANY_NAME', Tools::getValue('company_name'));
        Configuration::updateValue('CONTACT_ADDRESS', Tools::getValue('address'));
        Configuration::updateValue('CONTACT_PHONE', Tools::getValue('phone'));
        Configuration::updateValue('CONTACT_EMAIL', Tools::getValue('email'));
        Configuration::updateValue('CONTACT_BANK_INFO', Tools::getValue('bank_info'));

        // Speciální zpracování pro mapu - stejně jako v původní konfiguraci
        $map_embed_value = Tools::getValue('map_embed');
        if (!empty($map_embed_value) && strpos($map_embed_value, '<iframe') !== false) {
            // Uložíme jako base64 pro obejití HTML filtrů
            $encoded_map = base64_encode($map_embed_value);
            Configuration::updateValue('CONTACT_MAP_EMBED_ENCODED', $encoded_map);
            Configuration::updateValue('CONTACT_MAP_EMBED', $map_embed_value);
        } else {
            Configuration::updateValue('CONTACT_MAP_EMBED', $map_embed_value);
        }

        $output .= $this->displayConfirmation('Nastavení bylo úspěšně uloženo');
        return $output;
    }

    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);

        // Přidání vlastních stylů pokud potřeba
        $this->addCSS($this->module->getPathUri() . 'views/css/admin.css');
    }
}
