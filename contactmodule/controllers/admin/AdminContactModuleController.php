<?php
/**
 * Admin Controller pro Contact Module
 * Správa kontaktních údajů v administraci PrestaShop
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminContactModuleController extends ModuleAdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->meta_title = 'Správa kontaktních údajů';
    }

    public function initContent()
    {
        parent::initContent();

        // Zpracování formuláře
        if (Tools::isSubmit('submitContactConfiguration')) {
            $this->processForm();
        }

        // Zobrazení obsahu
        $this->content .= $this->renderConfigurationForm();
        $this->content .= $this->renderRepresentativesList();

        $this->context->smarty->assign('content', $this->content);
    }

    public function renderConfigurationForm()
    {
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => 'Základní kontaktní údaje',
                    'icon' => 'icon-cogs'
                ],
                'input' => [
                    [
                        'type' => 'text',
                        'label' => 'Název společnosti',
                        'name' => 'CONTACT_COMPANY_NAME',
                        'required' => true,
                        'col' => 6,
                    ],
                    [
                        'type' => 'textarea',
                        'label' => 'Adresa',
                        'name' => 'CONTACT_ADDRESS',
                        'required' => true,
                        'col' => 6,
                        'rows' => 4,
                    ],
                    [
                        'type' => 'text',
                        'label' => 'Telefon',
                        'name' => 'CONTACT_PHONE',
                        'required' => true,
                        'col' => 4,
                    ],
                    [
                        'type' => 'text',
                        'label' => 'Email',
                        'name' => 'CONTACT_EMAIL',
                        'required' => true,
                        'col' => 4,
                    ],
                    [
                        'type' => 'textarea',
                        'label' => 'Bankovní údaje',
                        'name' => 'CONTACT_BANK_INFO',
                        'col' => 6,
                        'rows' => 3,
                    ],
                    [
                        'type' => 'textarea',
                        'label' => 'Google Maps embed kód',
                        'name' => 'CONTACT_MAP_EMBED',
                        'desc' => 'Vložte celý iframe kód z Google Maps',
                        'col' => 12,
                        'rows' => 4,
                    ],
                ],
                'submit' => [
                    'title' => 'Uložit',
                    'class' => 'btn btn-default pull-right'
                ]
            ]
        ];

        // Načtení aktuálních hodnot
        $fields_value = [];
        foreach ($fields_form['form']['input'] as $field) {
            if (isset($field['name'])) {
                $fields_value[$field['name']] = Configuration::get($field['name']);
            }
        }

        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = 'configuration';
        $helper->module = $this->module;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);
        $helper->identifier = 'id_configuration';
        $helper->submit_action = 'submitContactConfiguration';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminContactModule', false);
        $helper->token = Tools::getAdminTokenLite('AdminContactModule');
        $helper->tpl_vars = [
            'fields_value' => $fields_value,
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        ];

        return $helper->generateForm([$fields_form]);
    }

    public function renderRepresentativesList()
    {
        $representatives = json_decode(Configuration::get('CONTACT_REPRESENTATIVES'), true);
        if (!$representatives) {
            $representatives = [];
        }

        $html = '<div class="panel">';
        $html .= '<div class="panel-heading">';
        $html .= '<i class="icon-group"></i> Obchodní zástupci';
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        
        if (empty($representatives)) {
            $html .= '<p>Žádní obchodní zástupci nejsou nastaveni.</p>';
        } else {
            $html .= '<div class="table-responsive">';
            $html .= '<table class="table table-striped">';
            $html .= '<thead>';
            $html .= '<tr>';
            $html .= '<th>Jméno</th>';
            $html .= '<th>Pozice</th>';
            $html .= '<th>Telefon</th>';
            $html .= '<th>Email</th>';
            $html .= '<th>Oblast</th>';
            $html .= '</tr>';
            $html .= '</thead>';
            $html .= '<tbody>';
            
            foreach ($representatives as $rep) {
                $html .= '<tr>';
                $html .= '<td>' . htmlspecialchars($rep['name']) . '</td>';
                $html .= '<td>' . htmlspecialchars($rep['position']) . '</td>';
                $html .= '<td>' . htmlspecialchars($rep['phone']) . '</td>';
                $html .= '<td>' . htmlspecialchars($rep['email']) . '</td>';
                $html .= '<td>' . htmlspecialchars($rep['region']) . '</td>';
                $html .= '</tr>';
            }
            
            $html .= '</tbody>';
            $html .= '</table>';
            $html .= '</div>';
        }
        
        $html .= '<p><em>Pro úpravu zástupců upravte konfiguraci v kódu modulu.</em></p>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

    public function processForm()
    {
        $errors = [];

        // Definice polí pro validaci
        $fields = [
            'CONTACT_COMPANY_NAME' => ['label' => 'Název společnosti', 'required' => true],
            'CONTACT_ADDRESS' => ['label' => 'Adresa', 'required' => true],
            'CONTACT_PHONE' => ['label' => 'Telefon', 'required' => true],
            'CONTACT_EMAIL' => ['label' => 'Email', 'required' => true, 'email' => true],
            'CONTACT_BANK_INFO' => ['label' => 'Bankovní údaje', 'required' => false],
            'CONTACT_MAP_EMBED' => ['label' => 'Google Maps embed kód', 'required' => false],
        ];

        // Validace a uložení jednotlivých polí
        foreach ($fields as $field_name => $field_config) {
            $value = Tools::getValue($field_name);

            // Základní validace
            if ($field_config['required'] && empty($value)) {
                $errors[] = sprintf('Pole "%s" je povinné', $field_config['label']);
                continue;
            }

            // Email validace
            if (isset($field_config['email']) && $field_config['email'] && !empty($value) && !Validate::isEmail($value)) {
                $errors[] = 'Neplatný formát emailu';
                continue;
            }

            // Uložení hodnoty
            Configuration::updateValue($field_name, $value);
        }

        if (empty($errors)) {
            $this->confirmations[] = 'Nastavení bylo úspěšně uloženo';
        } else {
            $this->errors = array_merge($this->errors, $errors);
        }
    }

    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);

        // Přidání vlastních stylů pokud potřeba
        $this->addCSS($this->module->getPathUri() . 'views/css/admin.css');
    }
}
